import { Card, CardContent } from "@/components/ui/card";
import {
   getAnimationProps,
   slideUpVariants,
   staggerContainerVariants,
   staggerItemVariants,
   viewportProps,
} from "@/lib/animations";
import {
   CalendarDaysIcon,
   ChatBubbleBottomCenterTextIcon,
   CheckBadgeIcon,
} from "@heroicons/react/24/solid";
import { motion } from "framer-motion";
import { Goal, GraduationCap } from "lucide-react";

export default function AboutSection() {
   const features = [
      {
         icon: CalendarDaysIcon,
         title: "12 live Zoom classes",
         description: "3 sessions per week",
      },
      {
         icon: Goal,
         title: "Weekly assignments",
         description: "Creative challenges",
      },
      {
         icon: ChatBubbleBottomCenterTextIcon,
         title: "Private Telegram group",
         description: "Questions, updates & submissions",
      },
      {
         icon: GraduationCap,
         title: "Practical lessons",
         description: "Step-by-step learning",
      },
   ];

   const skills = [
      "Trim, cut, and split clips smoothly",
      "Add text, captions & animated fonts",
      "Apply transitions, filters, overlays & effects",
      "Sync your edits perfectly to sounds",
      "Export high-quality TikTok-ready videos",
   ];

   return (
      <section id="about" className="py-20 bg-background">
         <div className="container mx-auto px-6">
            <div className="max-w-6xl mx-auto">
               {/* Section Header */}
               <motion.div
                  className="text-center mb-16"
                  {...getAnimationProps(slideUpVariants)}
               >
                  <h2 className="font-display text-4xl md:text-5xl font-bold text-foreground mb-6">
                     What You&apos;ll Get in This Class
                  </h2>
                  <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                     This 1-month online class (₦20,000 per student) is perfect
                     for absolute beginners. You&apos;ll progress from &quot;I
                     don&apos;t know how to split clips&quot; to confidently
                     creating smooth, shareable videos using CapCut.
                  </p>
               </motion.div>

               {/* Features Grid */}
               <motion.div
                  className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16"
                  {...getAnimationProps(staggerContainerVariants, 0.2)}
               >
                  {features.map((feature, index) => (
                     <motion.div key={index} variants={staggerItemVariants}>
                        <Card className="bg-card h-full border-1 shadow-lg hover:shadow-elegant transition-all duration-300 group rounded-2xl border-border/80">
                           <CardContent className="p-6 text-center">
                              <div className="mb-4 inline-flex p-4 bg-gradient-secondary rounded-full">
                                 <feature.icon className="w-7 h-7 text-white/90" />
                              </div>
                              <h3 className="font-semibold text-lg text-foreground mb-2">
                                 {feature.title}
                              </h3>
                              <p className="text-base text-muted-foreground">
                                 {feature.description}
                              </p>
                           </CardContent>
                        </Card>
                     </motion.div>
                  ))}
               </motion.div>

               {/* Skills Section */}
               <motion.div
                  className="bg-gradient-elegant border rounded-2xl p-8 md:p-12 shadow-lg"
                  {...getAnimationProps(slideUpVariants, 0.4)}
               >
                  <div className="max-w-4xl mx-auto">
                     <h3 className="font-display text-xl md:text-2xl font-bold text-center text-foreground mb-8">
                        By the End of This Class, You&apos;ll Know How To:
                     </h3>

                     <motion.div
                        className="grid grid-cols-1 md:grid-cols-2 gap-6"
                        variants={staggerContainerVariants}
                        initial="hidden"
                        whileInView="visible"
                        viewport={viewportProps}
                     >
                        {skills.map((skill, index) => (
                           <motion.div
                              key={index}
                              className="flex items-start gap-4"
                              variants={staggerItemVariants}
                           >
                              <div className="flex-shrink-0 mt-1">
                                 <CheckBadgeIcon className="w-6 h-6 text-secondary " />
                              </div>
                              <span className="text-foreground font-medium text-lg leading-relaxed">
                                 {skill}
                              </span>
                           </motion.div>
                        ))}
                     </motion.div>

                     {/* Tools Required */}
                     <motion.div
                        className="mt-10 text-center"
                        variants={staggerItemVariants}
                        initial="hidden"
                        whileInView="visible"
                        viewport={viewportProps}
                        transition={{ delay: 0.6 }}
                     >
                        <h3 className="font-display text-2xl font-bold text-foreground mb-6">
                           Tools Required
                        </h3>
                        <div className="flex flex-col sm:flex-row justify-center items-center gap-4">
                           <div className="bg-gradient-secondary rounded-lg px-6 py-4 shadow-soft border border-border/50">
                              <span className="font-semibold text-white">
                                 CapCut mobile app
                              </span>
                              <span className="text-gray-300 text-sm ml-2">
                                 (primary tool)
                              </span>
                           </div>
                           <div className="bg-gradient-secondary rounded-lg px-6 py-4 shadow-soft border border-border/50">
                              <span className="text-gray-300 text-sm">
                                 Optional:
                              </span>
                              <span className="font-medium text-white ml-2">
                                 CapCut desktop version
                              </span>
                           </div>
                        </div>
                     </motion.div>
                  </div>
               </motion.div>
            </div>
         </div>
      </section>
   );
}
