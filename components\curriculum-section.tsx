"use client";

import {
   Accordion,
   AccordionContent,
   AccordionItem,
   AccordionTrigger,
} from "@/components/ui/accordion";
import { Card, CardContent } from "@/components/ui/card";
import { curriculum } from "@/constants";
import {
   getAnimationProps,
   slideUpVariants,
   staggerContainerVariants,
   staggerItemVariants,
} from "@/lib/animations";
import { motion } from "framer-motion";
import { Goal, Star, TvIcon, Video } from "lucide-react";

const CurriculumSection = () => {
   return (
      <section className="py-20 bg-background" id="curriculum">
         <div className="container mx-auto px-4">
            {/* Section Header */}
            <motion.div
               className="text-center mb-16"
               {...getAnimationProps(slideUpVariants)}
            >
               <h2 className="font-display text-4xl md:text-5xl font-bold text-foreground mb-6">
                  Full Course Breakdown
               </h2>
               <p className="text-lg px-2 text-muted-foreground max-w-2xl mx-auto">
                  A structured 4-week journey from beginner to confident video
                  editor, with 3 live sessions per week.
               </p>
            </motion.div>

            <motion.div {...getAnimationProps(staggerContainerVariants, 0.2)}>
               <Accordion
                  type="single"
                  collapsible
                  className="space-y-6 max-w-4xl mx-auto"
               >
                  {curriculum.map((week) => (
                     <motion.div key={week.week} variants={staggerItemVariants}>
                        <AccordionItem
                           value={`week-${week.week}`}
                           className="border-0"
                        >
                           <Card className="overflow-hidden hover:shadow-md transition-all duration-300">
                              <AccordionTrigger className="hover:no-underline p-0">
                                 <div
                                    className={`w-full p-6 bg-gradient-elegant hover:opacity-90 transition-opacity relative`}
                                 >
                                    <div className="flex items-center justify-between w-full">
                                       <div className="grid grid-cols-[auto_1fr] items-center space-x-4">
                                          <div className="flex items-center justify-center w-12 h-12 bg-gradient-secondary text-primary rounded-full font-bold text-lg">
                                             {week.week}
                                          </div>
                                          <div className="text-left">
                                             <h3 className="text-lg md:text-xl font-serif font-bold text-black">
                                                Week {week.week}: {week.title}
                                             </h3>
                                             <p className="text-black/70 mt-1">
                                                {week.description}
                                             </p>
                                          </div>
                                       </div>
                                    </div>
                                 </div>
                              </AccordionTrigger>

                              <AccordionContent className="p-0">
                                 <CardContent className="p-6 bg-gradient-elegant">
                                    <div className="space-y-6">
                                       {week.classes.map((classItem) => (
                                          <div
                                             key={classItem.number}
                                             className="border-l-2 border-warm-accent pl-6 relative"
                                          >
                                             <div className="absolute -left-2 top-0 w-4 h-4 bg-secondary rounded-full"></div>

                                             <div className="space-y-3">
                                                <div className="flex items-start space-x-3">
                                                   <Video className="h-5 w-5 text-secondary mt-0.5" />
                                                   <div>
                                                      <span className="font-semibold text-black">
                                                         Class{" "}
                                                         {classItem.number}
                                                         {classItem.isFinal
                                                            ? " (Final)"
                                                            : ""}
                                                         :
                                                      </span>
                                                      <span className="text-black/80 ml-2">
                                                         {classItem.title}
                                                      </span>
                                                   </div>
                                                </div>

                                                {classItem.assignment && (
                                                   <div className="flex items-start space-x-3">
                                                      <Goal className="h-5 w-5 text-secondary mt-0.5" />
                                                      <div>
                                                         <span className="font-semibold text-black">
                                                            Assignment{" "}
                                                            {classItem.number}:
                                                         </span>
                                                         <span className="text-black/80 ml-2">
                                                            {
                                                               classItem.assignment
                                                            }
                                                         </span>
                                                      </div>
                                                   </div>
                                                )}

                                                {classItem.challenge && (
                                                   <div className="flex items-start space-x-3">
                                                      <Star className="h-5 w-5 text-warm-accent mt-0.5" />
                                                      <div>
                                                         <span className="font-semibold text-black">
                                                            {classItem.isFinal
                                                               ? "Final Challenge"
                                                               : "Challenge"}
                                                            :
                                                         </span>
                                                         <span className="text-black/80 ml-2">
                                                            {
                                                               classItem.challenge
                                                            }
                                                         </span>
                                                      </div>
                                                   </div>
                                                )}
                                             </div>
                                          </div>
                                       ))}
                                    </div>
                                 </CardContent>
                              </AccordionContent>
                           </Card>
                        </AccordionItem>
                     </motion.div>
                  ))}
               </Accordion>
            </motion.div>

            {/* Progress indicator */}
            <motion.div
               className="mt-12 text-center"
               {...getAnimationProps(slideUpVariants, 0.4)}
            >
               <div className="hidden md:inline-flex flex-col md:flex-row space-y-4 md:space-y-0 items-center space-x-4 bg-gradient-cold border border-border/50 shadow-md px-8 py-4 rounded-2xl md:rounded-full text-base font-semibold">
                  <div className="flex items-center space-x-2">
                     <TvIcon className="h-4 w-4 text-[#BB9781]" />
                     <span>12 Live Classes</span>
                  </div>
                  <div className="flex items-center space-x-2">
                     <Goal className="h-4 w-4 text-[#BB9781]" />
                     <span>8 Assignments</span>
                  </div>
                  <div className="flex items-center space-x-2">
                     <Star className="h-4 w-4 text-[#BB9781]" />
                     <span>4 Challenges</span>
                  </div>
               </div>
            </motion.div>
         </div>
      </section>
   );
};

export default CurriculumSection;
