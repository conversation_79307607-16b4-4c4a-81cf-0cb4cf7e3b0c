import { Card, CardContent } from "@/components/ui/card";
import { faqs } from "@/constants";
import {
   getAnimationProps,
   slideUpVariants,
   staggerContainerVariants,
   staggerItemVariants,
} from "@/lib/animations";
import { motion } from "framer-motion";
import { HelpCircle } from "lucide-react";
import {
   Accordion,
   AccordionContent,
   AccordionItem,
   AccordionTrigger,
} from "./ui/accordion";

export default function FAQSection() {
   return (
      <section className="py-20 bg-gradient-elegant">
         <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto">
               {/* Section Header */}
               <motion.div
                  className="text-center mb-16"
                  {...getAnimationProps(slideUpVariants)}
               >
                  <h2 className="font-display text-4xl md:text-5xl font-bold text-foreground mb-6">
                     Frequently Asked Questions
                  </h2>
                  <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                     Got questions? We&apos;ve got answers. Here are the most
                     common questions about our CapCut course.
                  </p>
               </motion.div>

               {/* FAQ Accordion */}
               <motion.div
                  {...getAnimationProps(staggerContainerVariants, 0.2)}
               >
                  <Accordion
                     type="single"
                     collapsible
                     className="space-y-4 max-w-4xl mx-auto"
                  >
                     {faqs.map((faq, index) => (
                        <motion.div key={index} variants={staggerItemVariants}>
                           <AccordionItem value={`faq-${index}`}>
                              <Card className="overflow-hidden border-0 shadow-soft hover:shadow-elegant transition-all duration-300">
                                 <AccordionTrigger className="w-full hover:no-underline p-0">
                                    <CardContent className="p-6 hover:bg-muted/30 transition-colors duration-200 rounded-lg bg-gradient-elegant border border-border/50 w-full">
                                       <div className="flex items-center justify-between text-left">
                                          <div className="flex items-center gap-4">
                                             <div className="flex-shrink-0">
                                                <HelpCircle className="w-6 h-6 text-[#BB9781]" />
                                             </div>
                                             <h3 className="text-lg font-medium text-foreground pr-4">
                                                {faq.question}
                                             </h3>
                                          </div>
                                       </div>
                                    </CardContent>
                                 </AccordionTrigger>
                                 <AccordionContent>
                                    <CardContent className="p-4 px-0">
                                       <div className="ml-9 text-muted-foreground leading-relaxed text-base">
                                          {faq.answer}
                                       </div>
                                    </CardContent>
                                 </AccordionContent>
                              </Card>
                           </AccordionItem>
                        </motion.div>
                     ))}
                  </Accordion>
               </motion.div>

               {/* Still Have Questions CTA */}
               <motion.div
                  className="mt-12 text-center"
                  {...getAnimationProps(slideUpVariants, 0.4)}
               >
                  <Card className="bg-black/80 border-0 shadow-elegant">
                     <CardContent className="p-8">
                        <h3 className="font-display text-2xl font-semibold text-white mb-4">
                           Still Have Questions?
                        </h3>
                        <p className="text-white/80 mb-6 max-w-md mx-auto">
                           Can&apos;t find what you&apos;re looking for? Our
                           team is here to help you make the right decision.
                        </p>
                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                           <a
                              href="mailto:<EMAIL>"
                              className="inline-flex items-center justify-center gap-2 bg-secondary text-secondary-foreground px-6 py-3 rounded-lg font-medium hover:bg-secondary/90 transition-colors duration-200"
                           >
                              Send an Email
                           </a>
                           <a
                              href="tel:08158546283"
                              className="inline-flex items-center justify-center gap-2 bg-primary text-primary-foreground px-6 py-3 rounded-lg font-medium hover:bg-primary/90 transition-colors duration-200"
                           >
                              Call Now
                           </a>
                        </div>
                        <div className="mt-6 text-center">
                           <p className="text-white/80 text-sm mb-3">
                              Already paid for the course?
                           </p>
                           <a
                              href="/confirm-payment"
                              className="inline-flex items-center justify-center gap-2 bg-white/10 text-white px-6 py-3 rounded-lg font-medium hover:bg-white/20 transition-colors duration-200 border border-white/20"
                           >
                              Confirm Your Payment
                           </a>
                        </div>
                     </CardContent>
                  </Card>
               </motion.div>
            </div>
         </div>
      </section>
   );
}
