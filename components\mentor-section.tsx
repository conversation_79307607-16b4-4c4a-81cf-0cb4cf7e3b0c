import { highlights } from "@/constants";
import {
   fadeInVariants,
   getAnimationProps,
   slideUpVariants,
   staggerContainerVariants,
   staggerItemVariants,
   viewportProps,
} from "@/lib/animations";
import { motion } from "framer-motion";
import { <PERSON>, <PERSON>rk<PERSON>, <PERSON> } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { Button } from "./ui/button";

export default function MentorSection() {
   return (
      <section className="py-20 bg-gradient-elegant" id="instructor">
         <div className="container mx-auto px-4">
            <motion.div
               className="text-center mb-20"
               {...getAnimationProps(slideUpVariants)}
            >
               <h2 className="font-display text-4xl md:text-5xl font-bold text-foreground mb-6">
                  <span className="animate-wave">👋</span> Meet Your Instructor
                  — Ose
               </h2>
               <p></p>
            </motion.div>

            <div className="grid lg:grid-cols-2 gap-12 items-center max-w-6xl mx-auto">
               {/* Instructor Photo */}
               <motion.div
                  className="relative order-2 lg:order-1 justify-self-center"
                  {...getAnimationProps(fadeInVariants, 0.2)}
               >
                  <div className="relative max-w-md">
                     <div className="absolute inset-0 bg-gradient-heading rounded-3xl transform rotate-3"></div>
                     <div className="relative bg-white rounded-3xl p-2 shadow-elegant transform -rotate-1 hover:rotate-0 transition-transform duration-300">
                        <Image
                           width={500}
                           height={500}
                           src="/images/ose.png"
                           alt="Ose - CreativaByOse Instructor"
                           className="w-full h-auto rounded-2xl"
                        />
                     </div>
                     {/* Floating badges */}
                     <div className="absolute -top-4 -right-4 bg-black text-secondary-foreground rounded-full p-3 animate-bounce-subtle-slow">
                        <Star className="h-6 w-6" />
                     </div>
                     <div
                        className="absolute -bottom-4 -left-4 bg-gradient-secondary text-secondary-foreground rounded-full p-3 animate-bounce-subtle-slow"
                        style={{ animationDelay: "1s" }}
                     >
                        <Award className="h-6 w-6" />
                     </div>
                     <div className="absolute -bottom-6 -right-6 bg-gradient-secondary text-secondary-foreground rounded-2xl p-4 shadow-floating animate-bounce-subtle-slow">
                        <div className="flex items-center gap-2">
                           <Award className="w-6 h-6" />
                           <div>
                              <div className="font-bold text-sm">Certified</div>
                              <div className="text-xs opacity-90">
                                 Video Editor
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
               </motion.div>

               {/* Instructor Info */}
               <motion.div
                  className="space-y-8 order-1 lg:order-2 max-w-lg mx-auto"
                  {...getAnimationProps(slideUpVariants, 0.4)}
               >
                  <motion.div
                     className="space-y-6"
                     variants={staggerItemVariants}
                  >
                     <p className="text-center md:text-start p-4 text-lg text-brand-black/80 leading-relaxed">
                        Hi! I&apos;m{" "}
                        <span className="font-semibold text-brand-black">
                           Ose
                        </span>
                        , the founder of
                        <span className="font-semibold text-brand-black">
                           {" "}
                           CreativaByOse
                        </span>
                        , a creative agency helps brands manage social media,
                        create amazing content, and connect with audiences
                        through influencer collaborations.
                     </p>
                  </motion.div>

                  <motion.div
                     className="space-y-4"
                     variants={staggerContainerVariants}
                     initial="hidden"
                     whileInView="visible"
                     viewport={viewportProps}
                  >
                     {highlights.map((highlight, index) => (
                        <motion.div
                           key={index}
                           className="flex items-start gap-5 px-4 group cursor-default"
                           variants={staggerItemVariants}
                        >
                           <div className="flex-shrink-0 mt-1">
                              <Sparkles className="w-6 h-6 text-[#BB9781] group-hover:text-yellow-400 transition-colors duration-300" />
                           </div>
                           <span className="text-foreground text-base font-medium leading-relaxed">
                              {highlight}
                           </span>
                        </motion.div>
                     ))}
                  </motion.div>

                  <motion.div
                     className="pt-2 text-center lg:text-end"
                     variants={staggerItemVariants}
                     initial="hidden"
                     whileInView="visible"
                     viewport={viewportProps}
                     transition={{ delay: 0.6 }}
                  >
                     <Link href="payment">
                        <Button variant="cta" size="lg">
                           Learn with Ose Today
                        </Button>
                     </Link>
                  </motion.div>
               </motion.div>
            </div>
         </div>
      </section>
   );
}
