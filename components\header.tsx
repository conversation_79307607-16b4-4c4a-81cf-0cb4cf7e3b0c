import Image from "next/image";
import Link from "next/link";
import { Button } from "./ui/button";

export default function Header() {
   return (
      <>
         {/* Header with Logo */}
         <header
            className="flex items-center justify-between px-12 py-6 bg-transparent absolute top-0 left-0 right-0 z-50 mix-blend-multiply"
            id="header"
         >
            <div className="flex items-center space-x-3">
               <Image
                  width={200}
                  height={200}
                  src="/images/logo.png"
                  alt="CreativaByOse Logo"
                  className="h-12 w-auto mix-blend-multiply"
               />
            </div>
            <Link href="#payment" className="hidden md:flex">
               <Button variant="cta">Enroll Now</Button>
            </Link>
         </header>
      </>
   );
}
