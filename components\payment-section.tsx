import {
   getAnimationProps,
   slideUpVariants,
   staggerContainerVariants,
   staggerItemVariants,
} from "@/lib/animations";
import { motion } from "framer-motion";
import { ArrowRight, CreditCard, Mail, Phone } from "lucide-react";
import Link from "next/link";
import { But<PERSON> } from "./ui/button";
import { Card, CardContent } from "./ui/card";

export default function PaymentSection() {
   const steps = [
      {
         number: 1,
         title: "Click 'Register Now'",
         description:
            "Find the registration button below and click to begin your creative journey.",
      },
      {
         number: 2,
         title: "Secure Your Spot",
         description:
            "Complete your payment for the ₦20,000 course fee using Paystack secure payment system.",
      },
      {
         number: 3,
         title: "Instant Access",
         description:
            "Upon payment confirmation, you'll receive an exclusive link to join the private Telegram group!",
      },
   ];

   return (
      <>
         <section
            className="py-24 min-h-screen bg-gradient-to-br from-black via-gray-900 to-black text-white relative overflow-hidden"
            id="payment"
         >
            <div className="absolute inset-0">
               <div className="absolute top-20 left-20 w-40 h-40 bg-[#fbeee6]/10 rounded-full" />
               <div className="absolute bottom-20 right-20 w-32 h-32 bg-[#fbeee6]/10 rounded-full" />
            </div>

            <div className="container mx-auto px-6 text-center relative z-10">
               <div className="max-w-4xl mx-auto">
                  <motion.h2
                     className="text-4xl md:text-5xl font-light mb-8"
                     {...getAnimationProps(slideUpVariants)}
                  >
                     How to{" "}
                     <span className="font-semibold">Register & Pay</span>
                  </motion.h2>

                  <motion.div
                     className="grid md:grid-cols-3 gap-8 my-16"
                     {...getAnimationProps(staggerContainerVariants, 0.2)}
                  >
                     {steps.map((step, index) => (
                        <motion.div key={index} variants={staggerItemVariants}>
                           <Card className="relative h-full group hover:shadow-lg transition-all duration-300 border border-white/10 bg-white/5 backdrop-blur-sm cursor-default">
                              <CardContent className="p-8 text-center">
                                 <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-primary text-black rounded-full flex items-center justify-center font-bold group-hover:scale-110 transition-transform text-xl">
                                    {step.number}
                                 </div>
                                 <div className="mt-4">
                                    <h3 className="font-semibold text-white mb-3 text-xl">
                                       {step.title}
                                    </h3>
                                    <p className="text-white/70 leading-relaxed">
                                       {step.description}
                                    </p>
                                 </div>
                              </CardContent>
                           </Card>
                        </motion.div>
                     ))}
                  </motion.div>

                  <motion.div {...getAnimationProps(slideUpVariants, 0.4)}>
                     <Link href="/payment">
                        <Button
                           variant={"elegant"}
                           size="lg"
                           className="px-8 py-6 text-lg mb-6 shadow-2xl font-semibold"
                        >
                           <CreditCard className="!h-6 !w-6 mr-2" />
                           Register Now - ₦20,000
                           <ArrowRight className="ml-3 h-6 w-6" />
                        </Button>
                     </Link>

                     <p className="text-sm mb-8 text-white/60 text-center max-w-80 mx-auto">
                        Secure payment via Paystack.
                     </p>

                     <div className="text-center">
                        <p className="text-white/70 text-sm mb-2">
                           Already paid?{" "}
                           <Link
                              href="/confirm-payment"
                              className="text-[#fbeee6] hover:text-white underline underline-offset-4 transition-colors duration-300"
                           >
                              Confirm your payment here
                           </Link>
                        </p>
                     </div>
                  </motion.div>

                  <motion.div
                     className="border-t border-white/20 pt-12"
                     {...getAnimationProps(slideUpVariants, 0.6)}
                  >
                     <h3 className="text-2xl font-semibold mb-8">
                        Need Help? Get In Touch
                     </h3>
                     <div className="flex flex-col sm:flex-row gap-8 justify-center items-center">
                        <Link
                           href="mailto:<EMAIL>"
                           className="flex items-center gap-3 text-lg"
                        >
                           <div className="bg-[#fbeee6]/20 p-3 rounded-full">
                              <Mail className="h-6 w-6 text-[#fbeee6]" />
                           </div>
                           <span><EMAIL></span>
                        </Link>
                        <Link
                           href="tel:08158546283"
                           className="flex items-center gap-3 text-lg"
                        >
                           <div className="bg-[#fbeee6]/20 p-3 rounded-full">
                              <Phone className="h-6 w-6 text-[#fbeee6]" />
                           </div>
                           <span>08158546283</span>
                        </Link>
                     </div>
                  </motion.div>
               </div>
            </div>
         </section>
      </>
   );
}
