import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import * as React from "react";

import { cn } from "@/lib/utils";

const buttonVariants = cva(
   "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium ring-offset-background transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 cursor-pointer",
   {
      variants: {
         variant: {
            default:
               "bg-primary text-primary-foreground hover:bg-primary/90 shadow-soft hover:shadow-elegant",
            destructive:
               "bg-destructive text-destructive-foreground hover:bg-destructive/90",
            outline:
               "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
            secondary:
               "bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-soft hover:shadow-elegant",
            ghost: "hover:bg-accent hover:text-accent-foreground",
            link: "text-primary underline-offset-4 hover:underline",
            cta: "bg-gradient-secondary text-secondary-foreground font-semibold shadow-elegant hover:shadow-floating transform transition-all duration-300",
            elegant:
               "bg-gradient-primary text-primary-foreground border border-border/50 shadow-soft hover:shadow-elegant transform",
         },
         size: {
            default: "h-11 px-6 py-3",
            sm: "h-9 rounded-md px-4",
            lg: "h-14 rounded-lg px-10 text-base",
            icon: "h-10 w-10",
            cta: "h-14 px-8 text-base font-semibold",
         },
      },
      defaultVariants: {
         variant: "default",
         size: "default",
      },
   }
);

export interface ButtonProps
   extends React.ButtonHTMLAttributes<HTMLButtonElement>,
      VariantProps<typeof buttonVariants> {
   asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
   ({ className, variant, size, asChild = false, ...props }, ref) => {
      const Comp = asChild ? Slot : "button";
      return (
         <Comp
            className={cn(buttonVariants({ variant, size, className }))}
            ref={ref}
            {...props}
         />
      );
   }
);
Button.displayName = "Button";

export { Button, buttonVariants };
