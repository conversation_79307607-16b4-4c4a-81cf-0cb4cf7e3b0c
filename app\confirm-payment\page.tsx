"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { FormField } from "@/components/ui/form-field";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import {
   getAnimationProps,
   slideUpVariants,
   staggerContainerVariants,
   staggerItemVariants,
} from "@/lib/animations";
import {
   ConfirmationFormData,
   confirmationFormSchema,
   ReferenceValidationResult,
   validateReference,
} from "@/lib/types/payment";
import { zodResolver } from "@hookform/resolvers/zod";
import { motion } from "framer-motion";
import {
   ArrowLeft,
   CheckCircle,
   Copy,
   ExternalLink,
   Search,
   XCircle,
} from "lucide-react";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

type ValidationStatus = "idle" | "validating" | "success" | "error";

export default function ConfirmPaymentPage() {
   const searchParams = useSearchParams();
   const referenceFromUrl = searchParams.get("reference");

   const [validationStatus, setValidationStatus] =
      useState<ValidationStatus>("idle");
   const [validationResult, setValidationResult] =
      useState<ReferenceValidationResult | null>(null);
   const [copiedReference, setCopiedReference] = useState(false);

   const {
      register,
      handleSubmit,
      formState: { errors, isValid },
      setValue,
   } = useForm<ConfirmationFormData>({
      resolver: zodResolver(confirmationFormSchema),
      mode: "onChange",
   });

   // Auto-validate if reference is provided in URL
   useEffect(() => {
      if (referenceFromUrl) {
         setValue("referenceId", referenceFromUrl);
         handleValidation(referenceFromUrl);
      }
   }, [referenceFromUrl, setValue]);

   const handleValidation = async (referenceId: string) => {
      setValidationStatus("validating");
      setValidationResult(null);

      try {
         const result = await validateReference(referenceId);
         setValidationResult(result);
         setValidationStatus(result.isValid ? "success" : "error");

         if (result.isValid) {
            toast.success(result.message);
         } else {
            toast.error(result.message);
         }
      } catch (error) {
         console.error(error);
         setValidationStatus("error");
         setValidationResult({
            isValid: false,
            message:
               "An error occurred while validating your reference. Please try again.",
         });
         toast.error("Validation failed. Please try again.");
      }
   };

   const onSubmit = async (data: ConfirmationFormData) => {
      await handleValidation(data.referenceId);
   };

   const copyReference = () => {
      if (referenceFromUrl) {
         navigator.clipboard.writeText(referenceFromUrl);
         setCopiedReference(true);
         toast.success("Reference ID copied to clipboard");
         setTimeout(() => setCopiedReference(false), 2000);
      }
   };

   const openTelegramLink = () => {
      if (validationResult?.telegramLink) {
         window.open(validationResult.telegramLink, "_blank");
      }
   };

   return (
      <div className="min-h-screen bg-gradient-elegant">
         <div className="container mx-auto px-6 py-8">
            {/* Header */}
            <motion.div
               className="flex items-center gap-4 mb-8"
               {...getAnimationProps(slideUpVariants)}
            >
               <Link
                  href="/#payment"
                  className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors duration-300"
               >
                  <ArrowLeft className="h-5 w-5" />
                  Back to Course Details
               </Link>
            </motion.div>

            <div className="max-w-2xl mx-auto">
               {/* Page Header */}
               <motion.div
                  className="text-center mb-12"
                  {...getAnimationProps(slideUpVariants)}
               >
                  <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
                     Confirm Your Payment
                  </h1>
                  <p className="text-lg text-muted-foreground">
                     {referenceFromUrl
                        ? "We're validating your payment. Please wait..."
                        : "Enter your transaction reference to confirm your payment and get access to the course."}
                  </p>
               </motion.div>

               {/* Success State */}
               {validationStatus === "success" && validationResult?.isValid && (
                  <motion.div
                     className="space-y-6"
                     {...getAnimationProps(staggerContainerVariants, 0.2)}
                  >
                     <motion.div variants={staggerItemVariants}>
                        <Card className="border-green-200 bg-green-50 shadow-elegant">
                           <CardContent className="p-8 text-center">
                              <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
                              <h2 className="text-2xl font-semibold text-green-800 mb-2">
                                 Payment Confirmed!
                              </h2>
                              <p className="text-green-700 mb-6">
                                 Welcome to the CapCut Mastery Course! You now
                                 have access to our private Telegram group.
                              </p>

                              {referenceFromUrl && (
                                 <div className="bg-white/50 rounded-lg p-4 mb-6">
                                    <p className="text-sm text-green-700 mb-2">
                                       Your Reference ID:
                                    </p>
                                    <div className="flex items-center justify-center gap-2">
                                       <code className="bg-white px-3 py-1 rounded text-green-800 font-mono">
                                          {referenceFromUrl}
                                       </code>
                                       <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={copyReference}
                                          className="text-green-700 hover:text-green-800"
                                       >
                                          {copiedReference ? (
                                             "Copied!"
                                          ) : (
                                             <Copy className="h-4 w-4" />
                                          )}
                                       </Button>
                                    </div>
                                    <p className="text-xs text-green-600 mt-2">
                                       Keep this reference for future
                                       confirmation
                                    </p>
                                 </div>
                              )}

                              <Button
                                 onClick={openTelegramLink}
                                 variant="elegant"
                                 size="lg"
                                 className="bg-green-600 hover:bg-green-700 text-white"
                              >
                                 <ExternalLink className="mr-2 h-5 w-5" />
                                 Join Telegram Group
                              </Button>
                           </CardContent>
                        </Card>
                     </motion.div>
                  </motion.div>
               )}

               {/* Error State */}
               {validationStatus === "error" &&
                  validationResult &&
                  !validationResult.isValid && (
                     <motion.div
                        className="space-y-6"
                        {...getAnimationProps(staggerContainerVariants, 0.2)}
                     >
                        <motion.div variants={staggerItemVariants}>
                           <Card className="border-red-200 bg-red-50 shadow-elegant">
                              <CardContent className="p-8 text-center">
                                 <XCircle className="h-16 w-16 text-red-600 mx-auto mb-4" />
                                 <h2 className="text-2xl font-semibold text-red-800 mb-2">
                                    Payment Not Found
                                 </h2>
                                 <p className="text-red-700 mb-6">
                                    {validationResult.message}
                                 </p>
                                 <Link href="/payment">
                                    <Button
                                       variant="elegant"
                                       size="lg"
                                       className="bg-red-600 hover:bg-red-700 text-white"
                                    >
                                       Pay for the Class
                                    </Button>
                                 </Link>
                              </CardContent>
                           </Card>
                        </motion.div>
                     </motion.div>
                  )}

               {/* Form for manual reference entry */}
               {!referenceFromUrl && validationStatus !== "success" && (
                  <motion.div {...getAnimationProps(slideUpVariants, 0.2)}>
                     <Card className="shadow-elegant border-0">
                        <CardHeader>
                           <CardTitle className="text-2xl font-semibold text-center">
                              Enter Your Reference ID
                           </CardTitle>
                        </CardHeader>
                        <CardContent>
                           <form
                              onSubmit={handleSubmit(onSubmit)}
                              className="space-y-6"
                           >
                              <FormField
                                 label="Transaction Reference ID"
                                 placeholder="Enter your reference ID (e.g., ABC1234567)"
                                 required
                                 {...register("referenceId")}
                                 error={errors.referenceId}
                                 description="You should have received this in your payment confirmation"
                              />

                              <Button
                                 type="submit"
                                 variant="elegant"
                                 size="lg"
                                 className="w-full"
                                 disabled={
                                    !isValid ||
                                    validationStatus === "validating"
                                 }
                              >
                                 {validationStatus === "validating" ? (
                                    <>
                                       <LoadingSpinner
                                          size="sm"
                                          className="mr-2"
                                       />
                                       Validating...
                                    </>
                                 ) : (
                                    <>
                                       <Search className="mr-2 h-5 w-5" />
                                       Confirm Payment
                                    </>
                                 )}
                              </Button>
                           </form>
                        </CardContent>
                     </Card>
                  </motion.div>
               )}

               {/* Loading State for URL reference */}
               {referenceFromUrl && validationStatus === "validating" && (
                  <motion.div {...getAnimationProps(slideUpVariants, 0.2)}>
                     <Card className="shadow-elegant border-0">
                        <CardContent className="p-8 text-center">
                           <LoadingSpinner size="lg" className="mx-auto mb-4" />
                           <h2 className="text-xl font-semibold mb-2">
                              Validating Your Payment
                           </h2>
                           <p className="text-muted-foreground">
                              Please wait while we confirm your payment...
                           </p>
                        </CardContent>
                     </Card>
                  </motion.div>
               )}
            </div>
         </div>
      </div>
   );
}
