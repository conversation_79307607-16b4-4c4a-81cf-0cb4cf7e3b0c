"use client";

import * as React from "react";
import { FieldError } from "react-hook-form";
import { cn } from "@/lib/utils";
import { Input } from "./input";

export interface FormFieldProps
   extends React.InputHTMLAttributes<HTMLInputElement> {
   label: string;
   error?: FieldError;
   description?: string;
}

const FormField = React.forwardRef<HTMLInputElement, FormFieldProps>(
   ({ className, label, error, description, id, ...props }, ref) => {
      const fieldId = id || `field-${label.toLowerCase().replace(/\s+/g, "-")}`;

      return (
         <div className="space-y-2">
            <label
               htmlFor={fieldId}
               className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
               {label}
               {props.required && <span className="text-destructive ml-1">*</span>}
            </label>
            <Input
               id={fieldId}
               ref={ref}
               className={cn(
                  error && "border-destructive focus-visible:ring-destructive",
                  className
               )}
               {...props}
            />
            {description && (
               <p className="text-sm text-muted-foreground">{description}</p>
            )}
            {error && (
               <p className="text-sm text-destructive">{error.message}</p>
            )}
         </div>
      );
   }
);
FormField.displayName = "FormField";

export { FormField };
