import {
   CalendarDaysIcon,
   PlayIcon,
   UserGroupIcon,
} from "@heroicons/react/24/solid";

type ClassItem = {
   number: number;
   title: string;
   assignment?: string;
   challenge?: string;
   isFinal?: boolean;
};

export const curriculum: {
   week: number;
   title: string;
   description: string;
   classes: ClassItem[];
}[] = [
   {
      week: 1,
      title: "CapCut Basics & Foundations",
      description: "Master the fundamentals and create your first videos",
      classes: [
         {
            number: 1,
            title: "Navigating the interface, essential trimming, splitting, and adding background music",
            assignment:
               "Create a 15-second video combining 3 clips with a background song",
         },
         {
            number: 2,
            title: "Adding dynamic text, animated fonts, and exploring CapCut templates",
            assignment:
               "Produce a TikTok video using a CapCut template and a custom caption",
         },
         {
            number: 3,
            title: "Utilizing basic transitions, understanding the timeline, and exporting clean videos",
            challenge:
               "Recreate a trending TikTok using simple cuts and transitions",
         },
      ],
   },
   {
      week: 2,
      title: "Audio Mastery, Effects & Perfect Syncing",
      description:
         "Enhance your videos with professional audio and visual effects",
      classes: [
         {
            number: 4,
            title: "Incorporating sound effects, voiceovers, and precise clip-to-beat synchronization",
            assignment:
               "Craft a 10-second edit perfectly synced to music or lyrics",
         },
         {
            number: 5,
            title: "Applying filters, essential color grading, basic speed ramping, and mastering slow/fast motion",
            assignment:
               "Create a compelling before-and-after transformation video with effects",
         },
         {
            number: 6,
            title: "Using stickers, emojis, overlays, and creating engaging POV-style edits",
            challenge: "Produce a relatable POV TikTok with text and effects",
         },
      ],
   },
   {
      week: 3,
      title: "Advanced Transitions, Text Effects & Storytelling",
      description: "Create cinematic transitions and compelling narratives",
      classes: [
         {
            number: 7,
            title: "Implementing smooth transitions (spin, zoom, swipe) for seamless edits",
            assignment:
               "Edit a 3-clip video using at least two smooth transitions",
         },
         {
            number: 8,
            title: "Exploring advanced text animations (typewriter, glitch, bounce) and syncing text to beats",
            assignment:
               "Develop a TikTok with animated text matching key moments",
         },
         {
            number: 9,
            title: "Editing for impactful storytelling (start, middle, end) and creating emotional/funny edits",
            challenge:
               "Produce a 30-second mini-vlog or story using transitions and effects",
         },
      ],
   },
   {
      week: 4,
      title: "Polishing & Content Flow",
      description:
         "Perfect your editing workflow and create professional content",
      classes: [
         {
            number: 10,
            title: "Adding professional logos/watermarks, utilizing trending sounds, and best export settings",
            assignment:
               "Create a branded or themed edit with your watermark/logo",
         },
         {
            number: 11,
            title: "Batch editing tips, planning content, and creating shot lists before editing",
            assignment:
               "Batch edit three short videos and submit your polished creations",
         },
         {
            number: 12,
            title: "Comprehensive review, live feedback session, bonus tips, Q&A, and exciting next steps",
            challenge:
               "Submit your BEST edit, post it on TikTok, and tag our class hashtag!",
            isFinal: true,
         },
      ],
   },
];

// Practicals and Assignments
export const practicalsAndAssignments = [
   {
      icon: CalendarDaysIcon,
      title: "Weekly Practicals",
      description: "Creative tasks and assignments to reinforce your learning",
      color: "from-[#fbeee6]/20 to-[#fbeee6]/5",
   },
   {
      icon: PlayIcon,
      title: "Live Feedback",
      description:
         "Get personalized feedback from instructor via live Zoom sessions",
      color: "from-[#fbeee6]/20 to-[#fbeee6]/5",
   },
   {
      icon: UserGroupIcon,
      title: "Private Telegram Group",
      description:
         "Private Telegram group with updates and submission tracking.",
      color: "from-[#fbeee6]/20 to-[#fbeee6]/5",
   },
];

// Instructor Highlights
export const highlights = [
   "I’m a skilled content creator, social media manager, and editor with hands-on experience creating viral TikTok videos and brand campaigns.",
   "I studied International Studies & Diplomacy at the University of Benin.",
   "I completed a 2-month digital marketing training at the Digital Marketing Skilled Institute. Social media management and content marketing from Coursera ",
   "I’m certified in video editing and passionate about teaching others how to bring their ideas to life through content.",
];

// FAQ
export const faqs = [
   {
      question: "Is this class suitable for total beginners?",
      answer:
         "Absolutely! This course is specifically designed for complete beginners. We start with the very basics of CapCut and gradually build up your skills. No prior video editing experience is required.",
   },
   {
      question: "How are classes delivered?",
      answer:
         "Classes are delivered via live Zoom sessions (3 times per week) with ongoing support through our private Telegram group. You'll get real-time interaction with the instructor and fellow students.",
   },
   {
      question: "Do I need a laptop?",
      answer:
         "No! CapCut mobile app is our primary tool, so you can learn entirely on your phone. However, we'll also cover the desktop version as an optional bonus for those who have access to a computer.",
   },
   {
      question: "What if I miss a live Zoom session?",
      answer:
         "Don't worry! All live sessions are recorded and made available to students. You'll get access to the recordings within 24 hours of each class, so you can catch up at your own pace.",
   },
   {
      question: "How long will I have access to the materials?",
      answer:
         "You'll have lifetime access to all course materials, recordings, and the private Telegram group. Even after the 4-week program ends, you can continue to reference the materials and stay connected with the community.",
   },
   {
      question: "Can I pay in installments?",
      answer:
         "Currently, we only accept full payment of ₦20,000 upfront. This helps us maintain the quality and structure of the program. However, we do offer a satisfaction guarantee - if you're not happy after the first week, you can get a full refund.",
   },
   {
      question: "What makes this different from YouTube tutorials?",
      answer:
         "Unlike scattered YouTube videos, this is a structured, progressive curriculum with live feedback, community support, and personalized guidance. You'll get direct access to an experienced instructor and a supportive peer group.",
   },
   {
      question:
         "Will I be able to create professional videos after this course?",
      answer:
         "Yes! By the end of the 4 weeks, you'll have the skills to create engaging, professional-looking videos for social media, personal projects, or even client work. We focus on practical, real-world applications.",
   },
   {
      question: "How do I confirm my payment after paying?",
      answer:
         "After completing your payment, you'll receive a unique reference ID. You can use this reference to confirm your payment and get instant access to the Telegram group. If you've already paid, you can confirm your payment at any time using the confirmation link on our payment section.",
   },
   {
      question: "What if I lose my payment reference ID?",
      answer:
         "Don't worry! If you lose your reference ID, you can contact us via email (<EMAIL>) or phone (08158546283) with your payment details, and we'll help you retrieve it. You can also try the payment confirmation page to manually enter your reference.",
   },
   {
      question: "How quickly will I get access after payment?",
      answer:
         "Access is instant! Once your payment is confirmed, you'll immediately receive the Telegram group link and can start interacting with other students and the instructor. The first class materials will be available in the group.",
   },
];
