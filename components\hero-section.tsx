import { Button } from "@/components/ui/button";
import {
   fadeInVariants,
   getAnimationProps,
   heroBadgeVariants,
   heroContentVariants,
   staggerContainerVariants,
   staggerItemVariants,
} from "@/lib/animations";
import {
   SparklesIcon,
   UserGroupIcon,
   VideoCameraIcon,
} from "@heroicons/react/24/solid";
import { motion } from "framer-motion";
import { <PERSON>R<PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import Link from "next/link";

export default function HeroSection() {
   return (
      <section className="relative min-h-[calc(100vh-46px)] bg-gradient-elegant flex flex-col items-center justify-center overflow-hidden mt-20 md:mt-12">
         {/* Background Decorative Elements */}
         <div className="absolute inset-0 opacity-40">
            <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-primary rounded-full blur-3xl animate-pulse"></div>
            <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-warm rounded-full blur-3xl animate-pulse delay-700"></div>
            <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-accent/30 rounded-full blur-2xl animate-bounce"></div>
         </div>

         {/* Background decoration */}
         <div className="absolute inset-0 opacity-5">
            <div className="absolute top-20 left-10 w-32 h-32 rounded-full bg-black animate-bounce-subtle-slow"></div>
            <div className="absolute bottom-20 right-10 w-24 h-24 rounded-full bg-black animate-bounce-subtle-slow"></div>
            <div className="absolute top-1/2 left-1/3 w-16 h-16 rounded-full bg-black animate-bounce-subtle-slow"></div>
         </div>

         <div className="container mx-auto px-6 py-20 pt-0 relative z-10">
            <motion.div
               className="max-w-4xl mx-auto text-center"
               {...getAnimationProps(staggerContainerVariants)}
            >
               <motion.div
                  className="inline-flex items-center gap-2 bg-[#fbeee6]/70 px-6 py-3 rounded-full mb-8"
                  variants={heroBadgeVariants}
               >
                  <SparklesIcon className="h-6 w-6 text-[#BB9781]" />
                  <span className="text-sm font-medium text-black">
                     Transform Your Video Skills in 30 Days
                  </span>
               </motion.div>
               {/* Main Headline */}
               <motion.h1
                  className="font-display text-5xl md:text-6xl lg:text-7xl font-bold text-foreground mb-6 leading-tight"
                  variants={heroContentVariants}
               >
                  Join My{" "}
                  <span className="bg-gradient-heading bg-clip-text text-transparent">
                     1-Month
                  </span>{" "}
                  Beginner{" "}
                  <span className="relative group cursor-default">
                     CapCut
                     <div className="absolute top-2 -right-2 animate-bounce-subtle-slow">
                        <Sparkles size={24} className="text-yellow-400" />
                     </div>
                     <div className="absolute bottom-2 -z-5 left-0 right-0 h-2 bg-gradient-heading rounded-full group-hover:scale-y-180 group-hover:scale-x-110 transition-transform"></div>
                  </span>{" "}
                  Editing Class!
               </motion.h1>

               {/* Subheadline */}
               <motion.h2
                  className="text-lg md:text-xl text-muted-foreground mb-10 font-normal max-w-3xl mx-auto leading-relaxed"
                  variants={staggerItemVariants}
               >
                  Learn how to confidently edit videos using CapCut — even if
                  you&apos;re starting from scratch! From complete beginner to
                  confident creator in just 4 weeks.
               </motion.h2>

               {/* CTAs */}
               <motion.div
                  className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12"
                  variants={fadeInVariants}
               >
                  <Link href="#payment">
                     <Button variant="cta" size="cta" className="group">
                        Register Now
                        <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                     </Button>
                  </Link>

                  <Link href="#about">
                     <Button
                        variant="elegant"
                        size="lg"
                        className="group bg-[#BB9781] text-white"
                     >
                        Learn More
                     </Button>
                  </Link>
               </motion.div>

               {/* Key Stats */}
               <motion.div
                  className="mt-16 hidden md:flex justify-center items-center space-x-8 text-black/40"
                  variants={fadeInVariants}
               >
                  <div className="flex items-center space-x-2">
                     <Star className="w-5 h-5 fill-current" />
                     <span className="text-base">12 Live Classes</span>
                  </div>
                  <div className="w-px h-6 bg-black/20" />
                  <div className="flex items-center space-x-2">
                     <VideoCameraIcon className="h-5 w-5" />
                     <span className="text-base">3 Sessions Per Week</span>
                  </div>
                  <div className="w-px h-6 bg-black/20" />
                  <div className="flex items-center space-x-2">
                     <UserGroupIcon className="h-5 w-5" />
                     <span className="text-base">Telegram Community</span>
                  </div>
               </motion.div>
            </motion.div>
         </div>
      </section>
   );
}
