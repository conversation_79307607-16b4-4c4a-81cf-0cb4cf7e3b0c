"use client";

// import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { FormField } from "@/components/ui/form-field";
// import { LoadingSpinner } from "@/components/ui/loading-spinner";
import {
   getAnimationProps,
   slideUpVariants,
   staggerContainerVariants,
   staggerItemVariants,
} from "@/lib/animations";
import {
   PaymentFormData,
   paymentFormSchema,
   PaymentStatus,
} from "@/lib/types/payment";
import { zodResolver } from "@hookform/resolvers/zod";
import { motion } from "framer-motion";
import { ArrowLeft, CreditCard, Shield, Users } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
// import { usePaystackPayment } from "react-paystack";
import { toast } from "sonner";

import dynamic from "next/dynamic";

const PaystackButtonClient = dynamic(
   () => import("@/components/paystack-button"),
   {
      ssr: false,
   }
);

export default function PaymentPage() {
   const router = useRouter();
   const [paymentStatus, setPaymentStatus] = useState<PaymentStatus>("idle");

   const publicKey = process.env.NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY;

   if (!publicKey) {
      throw new Error("Paystack public key is not defined");
   }

   const {
      register,
      // handleSubmit,
      formState: { errors, isValid },
      getValues,
   } = useForm<PaymentFormData>({
      resolver: zodResolver(paymentFormSchema),
      mode: "onChange",
   });

   const paystackConfig = {
      reference: new Date().getTime().toString(),
      name: getValues("fullName"),
      email: getValues("email"),
      amount: 2000000,
      publicKey,
      currency: "NGN",
   };

   const onSuccess = (reference: { reference: string }) => {
      console.log("Payment successful:", reference);
      setPaymentStatus("success");
      toast.success("Payment processed successfully!");
      // Redirect to confirmation page or show success message
      router.push(`/confirm-payment?reference=${reference.reference}`);
   };

   const onClose = () => {
      console.log("Payment cancelled");
      setPaymentStatus("idle");
      toast.error("Payment cancelled. Please try again.");
   };

   // const initializePayment = usePaystackPayment(paystackConfig);

   // const onSubmit = async (data: PaymentFormData) => {
   //    setPaymentStatus("processing");

   //    try {
   //       const result = await processPayment(data);

   //       if (result.success && result.referenceId) {
   //          setPaymentStatus("success");
   //          toast.success(result.message);

   //          // Redirect to confirmation page with reference
   //          router.push(`/confirm-payment?reference=${result.referenceId}`);
   //       } else {
   //          setPaymentStatus("error");
   //          toast.error(result.message);
   //       }
   //    } catch (error) {
   //       console.error(error);
   //       setPaymentStatus("error");
   //       toast.error("An unexpected error occurred. Please try again.");
   //    }
   // };

   const features = [
      {
         icon: Shield,
         title: "Secure Payment",
         description: "Your payment is processed securely via Paystack",
      },
      {
         icon: Users,
         title: "Instant Access",
         description: "Join the private Telegram group immediately",
      },
      {
         icon: CreditCard,
         title: "One-time Fee",
         description: "₦20,000 for the complete 4-week course",
      },
   ];

   return (
      <div className="min-h-screen bg-gradient-elegant">
         {/* Header */}
         <div className="container mx-auto px-6 py-8">
            <div className="flex items-center gap-4 mb-8">
               <Link
                  href="/#payment"
                  className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors duration-300"
               >
                  <ArrowLeft className="h-5 w-5" />
                  Back to Course Details
               </Link>
            </div>

            <div className="max-w-4xl mx-auto">
               {/* Page Header */}
               <motion.div
                  className="text-center mb-12"
                  {...getAnimationProps(slideUpVariants)}
               >
                  <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
                     Complete Your Registration
                  </h1>
                  <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                     You&apos;re just one step away from joining our CapCut
                     mastery course. Fill in your details below to proceed with
                     payment.
                  </p>
               </motion.div>

               <div className="grid lg:grid-cols-2 gap-12 items-start">
                  {/* Payment Form */}
                  <motion.div {...getAnimationProps(slideUpVariants, 0.2)}>
                     <Card className="shadow-elegant border-0">
                        <CardHeader>
                           <CardTitle className="text-2xl font-semibold">
                              Registration Details
                           </CardTitle>
                        </CardHeader>
                        <CardContent>
                           {/* <form
                              onSubmit={handleSubmit(onSubmit)}
                              className="space-y-6"
                           > */}
                           <div className="space-y-6">
                              <FormField
                                 label="Full Name"
                                 placeholder="Enter your full name"
                                 required
                                 {...register("fullName")}
                                 error={errors.fullName}
                              />

                              <FormField
                                 label="Email Address"
                                 type="email"
                                 placeholder="Enter your email address"
                                 required
                                 {...register("email")}
                                 error={errors.email}
                              />

                              <FormField
                                 label="Phone Number"
                                 type="tel"
                                 placeholder="Enter your phone number (optional)"
                                 description="We'll use this to send you course updates"
                                 {...register("phoneNumber")}
                                 error={errors.phoneNumber}
                              />

                              <PaystackButtonClient
                                 config={paystackConfig}
                                 paymentStatus={paymentStatus}
                                 isValid={isValid}
                                 onSuccess={onSuccess}
                                 onClose={onClose}
                              />

                              <p className="text-sm text-muted-foreground text-center">
                                 By proceeding, you agree to our terms and
                                 conditions. Your payment will be processed
                                 securely via Paystack.
                              </p>
                           </div>
                        </CardContent>
                     </Card>
                  </motion.div>

                  {/* Features */}
                  <motion.div
                     className="space-y-6"
                     {...getAnimationProps(staggerContainerVariants, 0.4)}
                  >
                     <div className="text-center lg:text-left">
                        <h3 className="text-2xl font-semibold mb-4">
                           What You Get
                        </h3>
                        <p className="text-muted-foreground">
                           Join hundreds of students who have transformed their
                           video editing skills
                        </p>
                     </div>

                     {features.map((feature, index) => (
                        <motion.div key={index} variants={staggerItemVariants}>
                           <Card className="border-0 shadow-soft hover:shadow-elegant transition-all duration-300">
                              <CardContent className="p-6">
                                 <div className="flex items-start gap-4">
                                    <div className="bg-primary/10 p-3 rounded-lg">
                                       <feature.icon className="h-6 w-6 text-primary" />
                                    </div>
                                    <div>
                                       <h4 className="font-semibold mb-2">
                                          {feature.title}
                                       </h4>
                                       <p className="text-muted-foreground">
                                          {feature.description}
                                       </p>
                                    </div>
                                 </div>
                              </CardContent>
                           </Card>
                        </motion.div>
                     ))}
                  </motion.div>
               </div>
            </div>
         </div>
      </div>
   );
}
