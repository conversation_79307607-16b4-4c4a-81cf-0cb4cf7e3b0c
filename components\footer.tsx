import {
   fadeInVariants,
   getAnimationProps,
   staggerContainerVariants,
   staggerItemVariants,
} from "@/lib/animations";
import { motion } from "framer-motion";
import { ArrowUp, Mail, Phone } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

const Footer = () => {
   return (
      <footer className="bg-black text-primary py-16 relative">
         {/* Background decoration */}
         <div className="hidden md:block absolute inset-0">
            <div className="absolute top-0 left-1/4 w-96 h-96 bg-[#fbeee6]/5 rounded-full blur-3xl" />
            <div className="absolute bottom-0 right-1/4 w-80 h-80 bg-[#fbeee6]/10 rounded-full blur-3xl" />
         </div>

         <div className="container max-w-7xl mx-auto px-8 relative z-5">
            <motion.div
               className="grid md:grid-cols-3 gap-8 mb-12 md:justify-items-center"
               {...getAnimationProps(staggerContainerVariants)}
            >
               {/* Brand */}
               <motion.div className="space-y-4" variants={staggerItemVariants}>
                  <div className="flex items-center space-x-3">
                     <Image
                        height={50}
                        width={50}
                        src="/images/logo.png"
                        alt="CreativaByOse Logo"
                        className="h-10 w-auto brightness-100 invert"
                     />
                  </div>
                  <p className="text-primary/80 leading-relaxed">
                     Empowering creators to bring their unique ideas to life
                     through dynamic video content. Master CapCut editing in
                     just one month with expert guidance.
                  </p>
               </motion.div>

               {/* Quick Links */}
               <motion.div className="space-y-4" variants={staggerItemVariants}>
                  <h4 className="font-semibold text-lg">Quick Navigation</h4>
                  <div className="space-y-2">
                     <Link
                        href="#about"
                        className="block text-primary/80 hover:text-primary transition-colors"
                     >
                        About the Course
                     </Link>
                     <Link
                        href="#curriculum"
                        className="block text-primary/80 hover:text-primary transition-colors"
                     >
                        Course Curriculum
                     </Link>
                     <Link
                        href="#instructor"
                        className="block text-primary/80 hover:text-primary transition-colors"
                     >
                        Meet Your Instructor
                     </Link>
                     <Link
                        href="#payment"
                        className="block text-primary/80 hover:text-primary transition-colors"
                     >
                        Enroll Now
                     </Link>
                  </div>
               </motion.div>

               {/* Contact Info */}
               <motion.div className="space-y-4" variants={staggerItemVariants}>
                  <h4 className="font-semibold text-lg">Get In Touch</h4>
                  <div className="space-y-3">
                     <div className="flex items-center space-x-3">
                        <Mail className="h-5 w-5 text-warm-accent" />
                        <a
                           href="mailto:<EMAIL>"
                           className="text-primary/80 hover:text-primary transition-colors"
                        >
                           <EMAIL>
                        </a>
                     </div>
                     <div className="flex items-center space-x-3">
                        <Phone className="h-5 w-5 text-warm-accent" />
                        <a
                           href="tel:08158546283"
                           className="text-primary/80 hover:text-primary transition-colors"
                        >
                           08158546283
                        </a>
                     </div>
                  </div>
               </motion.div>
            </motion.div>

            {/* Bottom Bar */}
            <motion.div
               className="border-t border-white/30 pt-8"
               {...getAnimationProps(fadeInVariants, 0.4)}
            >
               <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                  <div className="text-primary/60 text-sm">
                     © 2025 CreativaByOse. All rights reserved.
                  </div>

                  <Link
                     href="#header"
                     className="flex items-center space-x-2 text-primary/80 hover:text-primary transition-colors group"
                  >
                     <span className="text-sm">Back to Top</span>
                     <ArrowUp className="h-4 w-4 group-hover:-translate-y-1 transition-transform" />
                  </Link>
               </div>
            </motion.div>
         </div>
      </footer>
   );
};

export default Footer;
