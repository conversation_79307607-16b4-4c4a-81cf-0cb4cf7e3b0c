import { Toaster } from "@/components/ui/sonner";
import type { Metada<PERSON> } from "next";
import { Ralew<PERSON> } from "next/font/google";
import "./globals.css";

const raleway = Raleway({
   variable: "--font-raleway",
   subsets: ["latin"],
});

export const metadata: Metadata = {
   title: "Create Next App",
   description: "Generated by create next app",
};

export default function RootLayout({
   children,
}: Readonly<{
   children: React.ReactNode;
}>) {
   return (
      <html lang="en" className="scroll-smooth">
         <body className={`${raleway.className} antialiased`}>
            {children}
            <Toaster />
         </body>
      </html>
   );
}
