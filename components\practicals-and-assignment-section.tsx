import { practicalsAndAssignments } from "@/constants";
import {
   getAnimationProps,
   slideUpVariants,
   staggerContainerVariants,
   staggerItemVariants,
} from "@/lib/animations";
import { motion } from "framer-motion";
import { Card, CardContent } from "./ui/card";

export default function PracticalsAndAssignmentSection() {
   return (
      <>
         <section className="py-24 bg-[#f4efeb]">
            <div className="container max-w-7xl mx-auto px-6">
               <motion.div
                  className="text-center mb-16"
                  {...getAnimationProps(slideUpVariants)}
               >
                  <h2 className="font-display text-4xl md:text-5xl font-bold text-foreground mb-6">
                     Practicals, Assignments & Challenges
                  </h2>
                  <p className="text-lg px-2 text-muted-foreground max-w-2xl mx-auto">
                     Stay on track with weekly assignments and creative
                     challenges, get feedback via Zoom, and track your progress
                     using the Course Breakdown.
                  </p>
               </motion.div>

               <motion.div
                  className="grid md:grid-cols-3 gap-8"
                  {...getAnimationProps(staggerContainerVariants, 0.2)}
               >
                  {practicalsAndAssignments.map((item, index) => (
                     <motion.div key={index} variants={staggerItemVariants}>
                        <Card
                           className={`text-center border-1 border-border/40 bg-gradient-elegant shadow-lg rounded-3xl overflow-hidden h-full`}
                        >
                           <CardContent className="p-4 py-8 md:p-8">
                              <div className="bg-gradient-elegant border border-border/50 shadow-sm rounded-2xl size-16 lg:size-18 flex items-center justify-center mx-auto mb-8">
                                 <item.icon className="size-8 lg:size-9 text-black" />
                              </div>
                              <h3 className="text-xl lg:text-2xl font-semibold text-black mb-6">
                                 {item.title}
                              </h3>
                              <p className="text-gray-600 font-medium leading-relaxed">
                                 {item.description}
                              </p>
                           </CardContent>
                        </Card>
                     </motion.div>
                  ))}
               </motion.div>
            </div>
         </section>
      </>
   );
}
