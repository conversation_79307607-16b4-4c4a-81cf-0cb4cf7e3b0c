"use client";

import { But<PERSON> } from "@/components/ui/button";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { CreditCard } from "lucide-react";
import { usePaystackPayment } from "react-paystack";

interface PaystackButtonClientProps {
   // eslint-disable-next-line @typescript-eslint/no-explicit-any
   config: any;
   paymentStatus: string;
   isValid: boolean;
   onSuccess: (reference: { reference: string }) => void;
   onClose: () => void;
}

export default function PaystackButtonClient({
   config,
   paymentStatus,
   isValid,
   onSuccess,
   onClose,
}: PaystackButtonClientProps) {
   const initializePayment = usePaystackPayment(config);

   return (
      <Button
         type="button"
         variant="elegant"
         size="lg"
         className="w-full"
         disabled={!isValid || paymentStatus === "processing"}
         onClick={() => initializePayment({ onSuccess, onClose })}
      >
         {paymentStatus === "processing" ? (
            <>
               <LoadingSpinner size="sm" className="mr-2" />
               Processing Payment...
            </>
         ) : (
            <>
               <CreditCard className="mr-2 h-5 w-5" />
               Proceed to Payment - ₦20,000
            </>
         )}
      </Button>
   );
}
