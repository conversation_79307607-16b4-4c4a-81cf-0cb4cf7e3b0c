import { Variants } from "framer-motion";

// Check if user prefers reduced motion
export const prefersReducedMotion = () => {
   if (typeof window !== "undefined") {
      return window.matchMedia("(prefers-reduced-motion: reduce)").matches;
   }
   return false;
};

// Base animation variants
export const fadeInVariants: Variants = {
   hidden: {
      opacity: 0,
   },
   visible: {
      opacity: 1,
      transition: {
         duration: 0.6,
         ease: "easeOut",
      },
   },
};

export const slideUpVariants: Variants = {
   hidden: {
      opacity: 0,
      y: 30,
   },
   visible: {
      opacity: 1,
      y: 0,
      transition: {
         duration: 0.6,
         ease: "easeOut",
      },
   },
};

export const slideUpFastVariants: Variants = {
   hidden: {
      opacity: 0,
      y: 20,
   },
   visible: {
      opacity: 1,
      y: 0,
      transition: {
         duration: 0.4,
         ease: "easeOut",
      },
   },
};

// Staggered container variants
export const staggerContainerVariants: Variants = {
   hidden: {},
   visible: {
      transition: {
         staggerChildren: 0.1,
         delayChildren: 0.1,
      },
   },
};

export const staggerContainerFastVariants: Variants = {
   hidden: {},
   visible: {
      transition: {
         staggerChildren: 0.05,
         delayChildren: 0.05,
      },
   },
};

// Staggered item variants
export const staggerItemVariants: Variants = {
   hidden: {
      opacity: 0,
      y: 20,
   },
   visible: {
      opacity: 1,
      y: 0,
      transition: {
         duration: 0.4,
         ease: "easeOut",
      },
   },
};

// Hero section specific variants
export const heroContentVariants: Variants = {
   hidden: {
      opacity: 0,
      y: 20,
   },
   visible: {
      opacity: 1,
      y: 0,
      transition: {
         duration: 0.4,
         ease: "easeOut",
      },
   },
};

export const heroBadgeVariants: Variants = {
   hidden: {
      opacity: 0,
      scale: 0.9,
   },
   visible: {
      opacity: 1,
      scale: 1,
      transition: {
         duration: 0.5,
         ease: "easeOut",
      },
   },
};

// Card hover variants (for enhanced interactions)
export const cardHoverVariants: Variants = {
   rest: {
      y: 0,
      transition: {
         duration: 0.3,
         ease: "easeOut",
      },
   },
   hover: {
      y: -5,
      transition: {
         duration: 0.3,
         ease: "easeOut",
      },
   },
};

// Page transition variants
export const pageVariants: Variants = {
   initial: {
      opacity: 0,
   },
   in: {
      opacity: 1,
      transition: {
         duration: 0.6,
         ease: "easeOut",
      },
   },
   out: {
      opacity: 0,
      transition: {
         duration: 0.3,
         ease: "easeIn",
      },
   },
};

// Accessibility-aware animation function
export const getAnimationProps = (variants: Variants, delay: number = 0) => {
   if (typeof window !== "undefined" && prefersReducedMotion()) {
      return {
         initial: "visible",
         animate: "visible",
         variants: {
            visible: { opacity: 1, y: 0, x: 0, scale: 1 },
         },
      };
   }

   return {
      initial: "hidden",
      whileInView: "visible",
      viewport: { once: true, margin: "-50px" },
      variants,
      transition: { delay },
   };
};

// Viewport animation hook props
export const viewportProps = {
   once: true,
   margin: "-50px",
   amount: 0.1,
};

export const viewportPropsLarge = {
   once: true,
   margin: "-100px",
   amount: 0.2,
};
