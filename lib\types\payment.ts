import { z } from "zod";

// Payment form validation schema
export const paymentFormSchema = z.object({
   fullName: z
      .string()
      .min(2, "Full name must be at least 2 characters")
      .max(50, "Full name must be less than 50 characters")
      .regex(/^[a-zA-Z\s]+$/, "Full name can only contain letters and spaces"),
   email: z
      .string()
      .email("Please enter a valid email address")
      .min(1, "Email is required"),
   phoneNumber: z
      .string()
      .optional()
      .refine(
         (val) => {
            if (!val) return true; // Optional field
            // Nigerian phone number format validation
            const phoneRegex = /^(\+234|234|0)?[789][01]\d{8}$/;
            return phoneRegex.test(val.replace(/\s+/g, ""));
         },
         {
            message: "Please enter a valid Nigerian phone number",
         }
      ),
});

// Payment confirmation form validation schema
export const confirmationFormSchema = z.object({
   referenceId: z
      .string()
      .min(1, "Reference ID is required")
      .regex(/^[A-Z0-9]{8,12}$/, "Invalid reference format"),
});

// TypeScript types derived from schemas
export type PaymentFormData = z.infer<typeof paymentFormSchema>;
export type ConfirmationFormData = z.infer<typeof confirmationFormSchema>;

// Payment processing states
export type PaymentStatus = "idle" | "processing" | "success" | "error";

// Payment result interface
export interface PaymentResult {
   success: boolean;
   referenceId?: string;
   message: string;
   telegramLink?: string;
}

// Reference validation result
export interface ReferenceValidationResult {
   isValid: boolean;
   message: string;
   telegramLink?: string;
}

// Utility function to generate reference ID
export function generateReferenceId(): string {
   const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
   let result = "";
   for (let i = 0; i < 10; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
   }
   return result;
}

// Utility function to format phone number
export function formatPhoneNumber(phone: string): string {
   if (!phone) return "";
   
   // Remove all non-digit characters
   const cleaned = phone.replace(/\D/g, "");
   
   // Format as Nigerian number
   if (cleaned.startsWith("234")) {
      return `+${cleaned}`;
   } else if (cleaned.startsWith("0")) {
      return `+234${cleaned.slice(1)}`;
   } else if (cleaned.length === 10) {
      return `+234${cleaned}`;
   }
   
   return phone;
}

// Mock payment processing function
export async function processPayment(data: PaymentFormData): Promise<PaymentResult> {
   // Simulate API call delay
   await new Promise((resolve) => setTimeout(resolve, 2000));
   
   // Simulate 95% success rate
   const success = Math.random() > 0.05;
   
   if (success) {
      const referenceId = generateReferenceId();
      return {
         success: true,
         referenceId,
         message: "Payment processed successfully!",
         telegramLink: "https://t.me/+example_group_link",
      };
   } else {
      return {
         success: false,
         message: "Payment failed. Please try again.",
      };
   }
}

// Mock reference validation function
export async function validateReference(referenceId: string): Promise<ReferenceValidationResult> {
   // Simulate API call delay
   await new Promise((resolve) => setTimeout(resolve, 1000));
   
   // Mock validation - in real app, this would check against a database
   const validReferences = ["ABC1234567", "XYZ9876543", "TEST123456"];
   const isValid = validReferences.includes(referenceId) || referenceId.startsWith("PAY");
   
   if (isValid) {
      return {
         isValid: true,
         message: "Payment confirmed successfully!",
         telegramLink: "https://t.me/+example_group_link",
      };
   } else {
      return {
         isValid: false,
         message: "Reference code is incorrect. Please check the code again or click the button below to pay for the class.",
      };
   }
}
