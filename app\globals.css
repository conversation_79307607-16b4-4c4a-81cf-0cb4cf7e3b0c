@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
   --color-background: var(--background);
   --color-foreground: var(--foreground);
   --font-sans: var(--font-geist-sans);
   --font-mono: var(--font-geist-mono);
   --color-ring: var(--ring);
   --color-input: var(--input);
   --color-border: var(--border);
   --color-destructive: var(--destructive);
   --color-accent-foreground: var(--accent-foreground);
   --color-accent: var(--accent);
   --color-muted-foreground: var(--muted-foreground);
   --color-muted: var(--muted);
   --color-secondary-foreground: var(--secondary-foreground);
   --color-secondary: var(--secondary);
   --color-primary-foreground: var(--primary-foreground);
   --color-primary: var(--primary);
   --color-popover-foreground: var(--popover-foreground);
   --color-popover: var(--popover);
   --color-card-foreground: var(--card-foreground);
   --color-card: var(--card);
   --radius-sm: calc(var(--radius) - 4px);
   --radius-md: calc(var(--radius) - 2px);
   --radius-lg: var(--radius);
   --radius-xl: calc(var(--radius) + 4px);
}

:root {
   --radius: 0.625rem;
   --background: oklch(97.84% 0.0059 59.65);
   --foreground: oklch(26.86% 0 0);
   --card: oklch(98.61% 0.0034 67.78);
   --card-foreground: oklch(26.86% 0 0);
   --popover: oklch(98.61% 0.0034 67.78);
   --popover-foreground: oklch(26.86% 0 0);
   --primary: oklch(95.17% 0.0229 61.23);
   --primary-foreground: oklch(26.86% 0 0);
   --secondary: oklch(26.86% 0 0);
   --secondary-foreground: oklch(97.84% 0.0059 59.65);
   --muted: oklch(95.49% 0.0076 61.45);
   --muted-foreground: oklch(55.55% 0 0);
   --accent: oklch(92.87% 0.0264 61.99);
   --accent-foreground: oklch(26.86% 0 0);
   --destructive: oklch(0.577 0.245 27.325);
   --border: oklch(0.922 0 0);
   --input: oklch(0.922 0 0);
   --ring: oklch(0.708 0 0);
}

@layer base {
   * {
      @apply border-border outline-ring/50;
   }
   body {
      @apply bg-background text-foreground;
   }

   /* Creative Gradients */
   .bg-gradient-heading {
      background: linear-gradient(135deg, #bb9781, #dfbca7);
   }
   .bg-gradient-primary {
      background: linear-gradient(135deg, hsl(28 75% 93%), hsl(28 60% 88%));
   }
   .bg-gradient-secondary {
      background: linear-gradient(135deg, hsl(0 0% 15%), hsl(0 0% 25%));
   }
   .bg-gradient-warm {
      background: linear-gradient(135deg, hsl(28 75% 93%), hsl(28 85% 95%));
   }
   .bg-gradient-cold {
      background: linear-gradient(135deg, hsl(28 40% 98%), hsl(28 30% 94%));
   }
   .bg-gradient-elegant {
      background: linear-gradient(180deg, hsl(28 40% 98%), hsl(28 30% 94%));
   }

   .bg-gradient-elegant-reversed {
      background: linear-gradient(180deg, hsl(28 30% 94%), hsl(28 40% 98%));
   }

   /* Soft Shadows */
   .shadow-soft {
      box-shadow: 0 4px 20px -4px hsl(28 40% 85% / 0.3);
   }
   .shadow-elegant {
      box-shadow: 0 8px 30px -8px hsl(28 40% 80% / 0.25);
   }
   .shadow-floating {
      box-shadow: 0 20px 40px -12px hsl(28 40% 75% / 0.2);
   }

   /* Smooth Transitions */
   .transition-smooth {
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
   }
   .transition-spring {
      transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
   }

   /* Animations */
   .animate-bounce-subtle {
      animation: bounce-subtle 1s ease infinite;
   }

   .animate-bounce-subtle-slow {
      animation: bounce-subtle-slow 3s ease infinite;
   }

   .animate-wave {
      animation: wave 2s ease-in-out infinite;
   }

   @keyframes bounce-subtle {
      0%,
      100% {
         transform: translateY(0);
      }
      50% {
         transform: translateY(-4px);
      }
   }

   @keyframes bounce-subtle-slow {
      0%,
      100% {
         transform: translateY(0);
      }
      50% {
         transform: translateY(-10px);
      }
   }

   @keyframes wave {
      0% {
         transform: translateY(0);
      }
      50% {
         transform: translateY(-10px);
      }
      100% {
         transform: translateY(0);
      }
   }
}
